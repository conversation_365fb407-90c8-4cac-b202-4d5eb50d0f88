class TokenInfo {
  String? userId;

  String? workspaceId;

  String? workspaceThumbnail;

  String? avatar;

  String? displayName;

  String? accessToken;

  int? accessTokenExpiresAt;

  String? refreshToken;

  String? language;

  String? environment;

  // API Config
  String? webBaseUrl;
  String? apiBaseUrl;
  String? uploadBaseUrl;
  // String? secureBaseUrl;
  String? messengerBaseUrl;
  String? mqttTCPUrl;

  /// To use in gapo flutter submodule
  bool? useFlutterToken = false;

  // ms
  String? androidMSClientId;
  String? androidMSRedirectUrl;

  // user-agent
  String? userAgent;

  final FeatureInfo? featureInfo;

  final bool isCurrentAdminWorkspace;

  final SecureFeatureInfo? secureFeatureInfo;

  TokenInfo({
    this.userId = '',
    this.workspaceId = '',
    this.workspaceThumbnail,
    this.avatar = '',
    this.displayName = '',
    this.accessToken = '',
    this.refreshToken = '',
    this.language = 'vi',
    this.accessTokenExpiresAt,
    this.environment = 'staging',
    this.apiBaseUrl,
    this.messengerBaseUrl,
    this.mqttTCPUrl,
    this.uploadBaseUrl,
    this.webBaseUrl,
    this.useFlutterToken = false,
    this.androidMSClientId,
    this.androidMSRedirectUrl,
    this.userAgent,
    this.featureInfo,
    this.secureFeatureInfo,
    this.isCurrentAdminWorkspace = false,
  });

  factory TokenInfo.fromJson(Map<String, dynamic> json) {
    return TokenInfo(
      userId: json['userId'] as String?,
      workspaceId: json['workspaceId'] as String?,
      workspaceThumbnail: json['workspace_thumbnail'] as String?,
      avatar: json['avatar'] as String?,
      displayName: json['displayName'] as String?,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      language: json['language'] as String?,
      accessTokenExpiresAt: json['accessTokenExpiresAt'] as int?,
      environment: json['environment'] as String?,
      webBaseUrl: (json['web_base_url'] as String?)?.toUrl(),
      apiBaseUrl: (json['api_base_url'] as String?)?.toUrl(),
      uploadBaseUrl: (json['upload_base_url'] as String?)?.toUrl(),
      messengerBaseUrl: (json['messenger_base_url'] as String?)?.toUrl(),
      mqttTCPUrl: (json['mqtt_tcp_url'] as String?)?.toUrl(),
      useFlutterToken: json['useFlutterToken'] ?? false,
      androidMSClientId:
          json["msal"] != null && json["msal"]["client_id"] != null
              ? json["msal"]["client_id"]
              : "",
      androidMSRedirectUrl:
          json["msal"] != null && json["msal"]["redirect_url"] != null
              ? json["msal"]["redirect_url"]
              : "",
      userAgent: json["user-agent"],
      featureInfo: json["features"] != null
          ? FeatureInfo.fromJson(Map<String, dynamic>.from(json["features"]))
          : const FeatureInfo(),
      secureFeatureInfo: json['secure_feature_info'] != null
          ? SecureFeatureInfo.fromJson(json['secure_feature_info'])
          : null,
      isCurrentAdminWorkspace: json['is_current_admin_workspace'] != null
          ? _parseIsCurrentAdminWorkspace(json['is_current_admin_workspace'])
          : false,
    );
  }



  factory TokenInfo.fromJson(Map<String, dynamic> json) {
    return TokenInfo(
      userId: json['userId'] as String?,
      workspaceId: json['workspaceId'] as String?,
      workspaceThumbnail: json['workspace_thumbnail'] as String?,
      avatar: json['avatar'] as String?,
      displayName: json['displayName'] as String?,
      accessToken: json['accessToken'] as String?,
      refreshToken: json['refreshToken'] as String?,
      language: json['language'] as String?,
      accessTokenExpiresAt: json['accessTokenExpiresAt'] as int?,
      environment: json['environment'] as String?,
      webBaseUrl: (json['web_base_url'] as String?)?.toUrl(),
      apiBaseUrl: (json['api_base_url'] as String?)?.toUrl(),
      uploadBaseUrl: (json['upload_base_url'] as String?)?.toUrl(),
      messengerBaseUrl: (json['messenger_base_url'] as String?)?.toUrl(),
      mqttTCPUrl: (json['mqtt_tcp_url'] as String?)?.toUrl(),
      useFlutterToken: json['useFlutterToken'] ?? false,
      androidMSClientId:
          json["msal"] != null && json["msal"]["client_id"] != null
              ? json["msal"]["client_id"]
              : "",
      androidMSRedirectUrl:
          json["msal"] != null && json["msal"]["redirect_url"] != null
              ? json["msal"]["redirect_url"]
              : "",
      userAgent: json["user-agent"],
      featureInfo: json["features"] != null
          ? FeatureInfo.fromJson(Map<String, dynamic>.from(json["features"]))
          : const FeatureInfo(),
      secureFeatureInfo: json['secure_feature_info'] != null
          ? SecureFeatureInfo.fromJson(json['secure_feature_info'])
          : null,
      isCurrentAdminWorkspace: json['is_current_admin_workspace'] != null
          ? _parseIsCurrentAdminWorkspace(json['is_current_admin_workspace'])
          : false,
    );
  }

  static bool _parseIsCurrentAdminWorkspace(dynamic json) {
    /// ios lỗi khi truyền bool, nên pass tạm kiểu int.
    if (json is bool) {
      return json;
    } else if (json is int) {
      return json == 1; // 1 Admin, 0 Member
    }

    return false;
  }
}

final class FeatureInfo {
  const FeatureInfo({
    this.isEnableCopyPaste = true,
    this.isEnableScreenShot = true,
    this.isEnableDownloadFile = true,
  });

  final bool isEnableCopyPaste, isEnableScreenShot, isEnableDownloadFile;

  factory FeatureInfo.fromJson(dynamic json) {
    return FeatureInfo(
      isEnableCopyPaste:
          json['clipboard'] != null ? json['clipboard'] as bool : false,
      isEnableScreenShot: json['capture_screen'] != null
          ? json['capture_screen'] as bool
          : false,
      isEnableDownloadFile:
          json['download'] != null ? json['download'] as bool : false,
    );
  }
}

final class SecureFeatureInfo {
  const SecureFeatureInfo({
    this.isEnableForTask = false,
    this.isEnableForCalendar = false,
    this.isEnableForTicket = false,
    this.isEnableForTimeKeeping = false,
  });

  final bool isEnableForTask,
      isEnableForCalendar,
      isEnableForTicket,
      isEnableForTimeKeeping;

  factory SecureFeatureInfo.fromJson(dynamic json) {
    return SecureFeatureInfo(
      isEnableForTask: json['task'] != null ? json['task'] as bool : false,
      isEnableForCalendar:
          json['calendar'] != null ? json['calendar'] as bool : false,
      isEnableForTicket:
          json['ticket'] != null ? json['ticket'] as bool : false,
      isEnableForTimeKeeping:
          json['time_keeping'] != null ? json['time_keeping'] as bool : false,
    );
  }
}

extension on String {
  String toUrl() {
    if (endsWith("/")) {
      return substring(0, length - 1);
    } else {
      return this;
    }
  }
}
