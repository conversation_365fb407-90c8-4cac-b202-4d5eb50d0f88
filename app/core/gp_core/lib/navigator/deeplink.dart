import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';

Route? currentRoute;

void onPopInvoked(BuildContext context, bool didPop) {
  if (Platform.isIOS) {
    SystemNavigator.pop(animated: true);
    return;
  }

  if (currentRoute == null) {
    SystemNavigator.pop(animated: true);
    return;
  }

  if (currentRoute is GetPageRoute) {
    if ((currentRoute as GetPageRoute).canPop == false) {
      SystemNavigator.pop(animated: true);
      return;
    }
  }

  if (currentRoute?.isFirst == true) {
    SystemNavigator.pop(animated: true);
  } else {
    Navigator.pop(context);
  }
}

class DeepLinkMethod {
  static const openZoom = 'openZoom';
  static const setLanguage = 'setLanguage';
  static const updateTokenInfo = 'updateTokenInfo';
  static const updateUserInfo = 'updateUserInfo';
  static const createCalendar = 'createCalendar';

  /// use in case has only id
  /// e.g. from notification
  static const setEvent = 'setEvent';

  /// use in case has event object
  /// e.g. from calendar list
  static const openFindingColleagues = 'openFindingColleagues';

  ///
  static const syncSuccessfully = 'syncSuccessfully';

  /// Use in PlatformNavigator
  static const performNavigation = 'performNavigation';
  static const setPlatformNavigatorParams = 'setPlatformNavigatorParams';

  /// MSAL Framework
  static const msalAcquireTokenInteractively = 'msalAcquireTokenInteractively';
  static const msalAcquireCurrentAccountTokenSilently =
      'msalAcquireCurrentAccountTokenSilently';
  static const msalLoadCurrentAccount = 'msalLoadCurrentAccount';
  static const refreshOutlookSyncIfNeeded = 'refreshOutlookSyncIfNeeded';
  static const initializeAssigneePicker = 'initializeAssigneePicker';
  static const initializeRtfEditor = 'initializeRtfEditor';

  // collab
  static const initializeTaskCollab = 'initializeTaskCollab';
  static const initializeCalendarCollab = 'initializeCalendarCollab';
  static const refreshTaskCollab = 'refreshTaskCollab';
  static const initTaskListData = 'initTaskListData';
  static const scrollToTop = 'scrollToTop';

  /// Others
  static const logout = 'logout';

  static const updateSwipeBackGestureIfNeeded =
      'updateSwipeBackGestureIfNeeded';

  static const createTaskFromMessage = "createTaskFromMessage";
  static const updateWorkspace = "trySwitchWorkspace";
}

class Deeplink {
  static const MethodChannel channel = MethodChannel('gapowork');

  static Future<dynamic> flutterInit() async {
    if (Platform.isIOS) {
      try {
        final result = await channel.invokeMethod("flutterInit");
        return result;
      } catch (e, s) {
        GPCoreTracker().appendError(
          'Flutter:main: flutterInit error',
          data: {'error': e, 'stacktrace': s},
        );
        // _catchDeepLinkError(e, s);
        return e;
      }
    }
  }

  static Future<bool> requestStoragePermissions() async {
    if (GetPlatform.isAndroid) {
      final permissions = <String>[
        "android.permission.WRITE_EXTERNAL_STORAGE",
        "android.permission.MANAGE_EXTERNAL_STORAGE",
        "android.permission.READ_EXTERNAL_STORAGE"
      ];

      // if (Constants.getAndroidSdkInt() < 33) {
      //   permissions.add("android.Manifest.permission.READ_EXTERNAL_STORAGE");
      // } else if (Constants.getAndroidSdkInt() >= 33) {
      //   permissions.add("android.Manifest.permission.READ_MEDIA_IMAGES");
      //   permissions.add("android.Manifest.permission.READ_MEDIA_VIDEO");
      //   permissions.add("android.Manifest.permission.READ_MEDIA_AUDIO");
      // }
      return await Deeplink.requestPermission(permissions);
    }
    
    return true;
  }

  static Future<dynamic> signInGoogle() async {
    try {
      final result = await channel.invokeMethod("signInGoogle");
      return result;
    } catch (e, s) {
      _catchDeepLinkError(e, s);
      return e;
    }
  }

  static Future<dynamic> requestGoogleScopes({List<String>? scopes}) async {
    try {
      final arguments = {"scopes": scopes};
      final result =
          await channel.invokeMethod("requestGoogleScopes", arguments);
      return result;
    } catch (e, s) {
      _catchDeepLinkError(e, s);

      return e;
    }
  }

  static Future<dynamic> signOutGoogle() async {
    final result =
        await channel.invokeMethod("signOutGoogle").catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
    return result;
  }

  static void switchToCalendar() {
    channel.invokeMethod("switchToCalendar").catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
  }

  static void refreshEventList() {
    channel.invokeMethod("refreshEventList").catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
  }

  // static Future _gotoCalendarCreate(dynamic arguments) {
  //   return channel.invokeMethod("calendarCreate", arguments).catchError((e, s) {
  //     _catchDeepLinkError(e, s);
  //   });
  // }

  // static Future _gotoCalendarDetail(dynamic arguments) {
  //   return channel.invokeMethod("calendarEdit", arguments).catchError((e, s) {
  //     _catchDeepLinkError(e, s);
  //   });
  // }

  static void openImageViewer(List<GMedia> medias) {
    List<Map<String, dynamic>> arguments =
        medias.map((e) => e.toJson()).toList();
    channel.invokeMethod("openImageViewer", arguments).catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
  }

  static Future<FilePickerResult?> openImagePicker({int limit = 16}) async {
    final List<Map>? result = await channel.invokeListMethod("openImagePicker",
        {"limit_media": limit, "media_max_selectable": limit});
    final List<PlatformFile> platformFiles = <PlatformFile>[];

    if (result == null) return null;

    for (final Map platformFileMap in result) {
      platformFiles.add(
        PlatformFile.fromMap(
          platformFileMap,
        ),
      );
    }

    return FilePickerResult(platformFiles);
  }

  static Future onTapUrl(String? href) async {
    if (href != null && href.isNotEmpty) {
      if (RegExp(Pattern.emailPattern).hasMatch(href)) {
        final Uri emailLaunchUri = Uri(
          scheme: 'mailto',
          path: href,
        );

        return await launchUrl(emailLaunchUri);
      } else if (RegExp(Pattern.phonePatternAllCase).hasMatch(href)) {
        return await launchUrl(Uri.parse("tel://$href"));
      }

      // decode full để decode các kí tự đặc biệt..., encode lại để tránh link có tiếng việt
      href = Uri.decodeFull(href);
      href = Uri.encodeFull(href);

      // if (GetPlatform.isAndroid) {
      //   // android đăng ký url schema rồi, nên chỉ cần launch url là có tuỳ chọn mở trình duyệt hoặc GapoWork
      //   return await launcher.launchUrl(Uri.parse(href),
      //       mode: launcher.LaunchMode.externalApplication);
      // } else {
      //   // buộc phải gọi qua native xử lý deepLink
      //   Deeplink.launchUrl(href);
      // }

      Deeplink.openUrl(href);

      return null;
    }
  }

  static void openUrl(String url) {
    // if (GetPlatform.isAndroid) {
    //   final Uri uri = Uri.parse(url);

    //   String urlAndroid;

    //   String deepLinkHost = "";

    //   if (url.contains("staging")) {
    //     deepLinkHost = "staging-work.gapo";
    //   } else if (url.contains("uat")) {
    //     deepLinkHost = "uat-work.gapo";
    //   } else if (url.contains(".gapowork.app")) {
    //     deepLinkHost = "work-op";
    //   } else if (url.contains(".gapowork.vn")) {
    //     deepLinkHost = "work.gapo";
    //   }

    //   final bool isDeepLinkHost = deepLinkHost.isNotEmpty;

    //   if (isDeepLinkHost) {
    //     urlAndroid = "${getDeepLinkHost()}:/${uri.path}";
    //   } else {
    //     urlAndroid = url;
    //   }

    //   launchUrl(Uri.parse(urlAndroid));
    //   if (isDeepLinkHost) {
    //     Future.delayed(const Duration(seconds: 1))
    //         .then((value) => closeInAppWebView());
    //   } else {
    //     launchUrl(uri, mode: LaunchMode.externalApplication);
    //     return;
    //   }
    //   // launcher.launchUrl(Uri.parse(urlAndroid),
    //   //     mode: launcher.LaunchMode.externalApplication);
    // } else {
    //   final params = {"url": url};
    //   channel.invokeMethod("openInAppDeeplink", params);
    // }

    final params = {"url": url};
    channel.invokeMethod("openInAppDeeplink", params).catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
  }

  static void openFilePreview(String fileURL) {
    final arguments = {'url': fileURL};
    channel.invokeListMethod('openFilePreview', arguments).catchError((e, s) {
      _catchDeepLinkError(e, s);

      return null;
    });
  }

  static void openUser(String userId) async {
    // work.gapo://user/689327817
    String url = "${getDeepLinkHost()}://user/$userId";

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      // https://uat.gapowork.vn/profile/689327817
      await launchUrl(Uri.parse("${Constants.appDomain}/profile/$userId"));
    }
  }

  static String getDeepLinkHost() {
    String deepLinkHost = "work.gapo";

    if (Constants.appDomain.contains("staging")) {
      deepLinkHost = "staging-work.gapo";
    } else if (Constants.appDomain.contains("uat")) {
      if (Platform.isIOS) {
        deepLinkHost = "work.gapo";
      } else {
        deepLinkHost = "uat-work.gapo";
      }
    }

    if (!Constants.appDomain.contains("gapowork.com") &&
        !Constants.appDomain.contains("gapowork.vn")) {
      deepLinkHost = "work-op";
    }

    return deepLinkHost;
  }

  static void openChatWithUser(String userId) async {
    String url = "${getDeepLinkHost()}://messenger/user/$userId";
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  static void openHashtag(String tag) async {
    String url = "${getDeepLinkHost()}://tag/$tag";
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  static void enableNativeSwipeBackGesture() {
    if (GetPlatform.isIOS) {
      channel.invokeMethod('enableSwipeBackGesture').catchError((e, s) {
        _catchDeepLinkError(e, s);
      });
    }
  }

  static void disableNativeSwipeBackGesture() {
    if (GetPlatform.isIOS) {
      channel.invokeMethod('disableSwipeBackGesture').catchError((e, s) {
        _catchDeepLinkError(e, s);
      });
    }
  }

  static void sendScreenTrackingEvent(String screenName) {
    if (!FeatureFlag.isFromNative) return;

    logDebug('------------------------------------------------------------\n'
        'TRACK SCREEN: $screenName\n'
        '------------------------------------------------------------');
    final params = {'screenName': screenName};
    channel.invokeMethod('sendScreenTracking', params).catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
  }

  static void recordError({
    required Object error,
    StackTrace? stackTrace,
  }) {
    channel.invokeMethod(
      'captureSentry',
      {
        'title': 'Flutter:core: Handle exception',
        'error_message':
            'error:${error.toString()}\n stackTrace:${stackTrace.toString()}',
      },
    ).catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
  }

  static void _catchDeepLinkError(Object e, StackTrace s) {
    logDebug('_catchDeepLinkError -> \n e:$e \n s:$s');
    GPCoreTracker().appendError(
      'Flutter:core: Handle DeepLink error',
      data: {'error': e, 'stacktrace': s},
    );
  }

  static Future<bool> requestPermission(List<String> permissions) async {
    var result = await channel.invokeMethod(
        'requestPermissions', {"permissions": permissions}).catchError((e, s) {
      logDebug("test: requestPermission error: ${e.toString()}");
      _catchDeepLinkError(e, s);
    });

    if (result is Map) {
      bool grantedAllPermissions = result["permissions"]
          .keys
          .every((element) => permissions.contains(element));
      if (grantedAllPermissions) {
        // Toàn bộ permissions được grandted
        return true;
      }
    }

    return false;
  }

  static void openZoom({required String id, required String url}) {
    final params = {
      'meeting_url': url,
    };
    if (GetPlatform.isIOS) {
      params.addAll({
        "meeting_id": id,
      });
    }
    channel.invokeMethod(DeepLinkMethod.openZoom, params).catchError((e, s) {
      _catchDeepLinkError(e, s);
    });
    // disableNativeSwipeBackGesture();
  }

  static Future renewToken(
    Function(Object e, StackTrace s) errorCallback,
  ) {
    return channel.invokeMethod('renewToken').catchError(errorCallback);
  }
}
