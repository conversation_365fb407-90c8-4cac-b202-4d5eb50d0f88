import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/configs/token_manager.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_core/utils/log.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../constants/web_constant.dart';

final class ProjectManagementController {
  final webviewController = WebViewController();

  String getWebUrl() {
    if (Constants.isStaging) {
      return GPWebConstants.kWebviewStagingUrl
          .replaceAll('{workspace_id}', Constants.workspaceId());
    }

    return GPWebConstants.kWebviewUrl
        .replaceAll('{workspace_id}', Constants.workspaceId());
  }

  Future injectTokenToWeb() async {
    final token = await TokenManager.accessToken();

    final escapedToken = token.replaceAll("'", "\\'").replaceAll('"', '\\"');

    webviewController.runJavaScript(
        '${GPWebConstants.kWindowReceiveTokenMethod}("$token");');

    final jsCodeSetToken = '''
      window.postMessage({
        type: ${GPWebConstants.kWebPostMessageFlutterSyncAuthRequest},
        token: $escapedToken }, "*");
    ''';

    webviewController.runJavaScript(jsCodeSetToken);
  }

  Future changeWebLanguage() async {
    final token = await TokenManager.accessToken();

    final languageCode = Constants.language();

    webviewController.runJavaScript(
        '${GPWebConstants.kWindowReceiveTokenMethod}("$token");');

    final jsCodeChangeLanguage = '''
      window.postMessage({
        type: ${GPWebConstants.kWebPostMessageLanguageChangedRequest},
        token: $languageCode }, "*");
    ''';

    webviewController.runJavaScript(jsCodeChangeLanguage);
  }

  void onMessageReceived(JavaScriptMessage message) {
    final mess = message.message;
    if (mess == GPWebConstants.kTokenSuccess) {
      GPCoreTracker().appendMessage(
        'Flutter:projectManagement.onMessageReceived: Token handled successfully!',
      );
    } else if (mess == GPWebConstants.kWebChannelError) {
      GPCoreTracker().appendError(
        'Flutter:projectManagement.onMessageReceived: Web: Communication channel GPWebChannel not found.',
      );
    } else if (mess == GPWebConstants.kTokenError) {
      GPCoreTracker().appendError(
        'Flutter:projectManagement.onMessageReceived: Web: ReceiveToken was called but no token was provided.',
      );
    }
  }

  void onHttpError(HttpResponseError error) {
    GPCoreTracker().appendError(
      'Flutter:core.onHttpError:error',
      data: {
        'error request': error.request,
        'error response': error.response,
      },
    );
  }

  void onConsoleMessage(JavaScriptConsoleMessage message) {
    final mess = message.message;
    logDebug('DEBUG: log from web: $mess');

    GPCoreTracker().appendMessage(
      'Flutter:projectManagement.onConsoleMessage: Token handled successfully!',
    );
  }
}
